2025-08-02 08:28:59,081 - __main__ - WARNING - Slow command execution: watchlist took 6.51s
2025-08-02 08:33:33,782 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=8f4c8e4f149015e70e5d621ed55a8e8c524ee1a6fa907af223baa313699a5177
2025-08-02 08:33:33,783 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=8f4c8e4f149015e70e5d621ed55a8e8c524ee1a6fa907af223baa313699a5177 (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=8f4c8e4f149015e70e5d621ed55a8e8c524ee1a6fa907af223baa313699a5177)
2025-08-02 08:35:40,871 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=0122fd940bea2288bbfb802fd5c67f663c1890e5a92e03468fdab226ee867629
2025-08-02 08:35:40,871 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=0122fd940bea2288bbfb802fd5c67f663c1890e5a92e03468fdab226ee867629 (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=0122fd940bea2288bbfb802fd5c67f663c1890e5a92e03468fdab226ee867629)
2025-08-02 08:39:53,022 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=9b2dca208a5deb28894c3f78fdfc9c502cc8a46b99d980aba0dbf3a1de3ea9e3
2025-08-02 08:39:53,023 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=9b2dca208a5deb28894c3f78fdfc9c502cc8a46b99d980aba0dbf3a1de3ea9e3 (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=9b2dca208a5deb28894c3f78fdfc9c502cc8a46b99d980aba0dbf3a1de3ea9e3)
2025-08-02 09:24:46,728 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=e1e7f6da446683420c4203b24374195a4796cecb3f59624724b9b8eeab24f748
2025-08-02 09:24:46,728 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=e1e7f6da446683420c4203b24374195a4796cecb3f59624724b9b8eeab24f748 (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=e1e7f6da446683420c4203b24374195a4796cecb3f59624724b9b8eeab24f748)
2025-08-02 13:45:21,625 - handlers.discord.admin.admin_commands - WARNING - Admin fantastic_beagle_96376 cleared 4 messages from #paxg (keep_pinned: True)
2025-08-02 14:04:32,973 - discord.gateway - WARNING - Can't keep up, shard ID None websocket is 43.3s behind.
2025-08-02 14:59:46,188 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=e341bae24f7650254df0ff64ef07619b8262bdcafd41e1803ffb48c89c0d3515
2025-08-02 14:59:46,189 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=e341bae24f7650254df0ff64ef07619b8262bdcafd41e1803ffb48c89c0d3515 (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=e341bae24f7650254df0ff64ef07619b8262bdcafd41e1803ffb48c89c0d3515)
2025-08-02 15:07:16,442 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=bb242c4793b6cbc51878053dafed22078edebff2a02bdd20eb9e1ec9171eb9a2
2025-08-02 15:07:16,442 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=bb242c4793b6cbc51878053dafed22078edebff2a02bdd20eb9e1ec9171eb9a2 (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=bb242c4793b6cbc51878053dafed22078edebff2a02bdd20eb9e1ec9171eb9a2)
2025-08-02 15:25:01,384 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=ecbc0e1546c9737f6ba1b3b69d2e242710568707b8ea4fdd9250a3f00e92c84c
2025-08-02 15:25:01,384 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=ecbc0e1546c9737f6ba1b3b69d2e242710568707b8ea4fdd9250a3f00e92c84c (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=ecbc0e1546c9737f6ba1b3b69d2e242710568707b8ea4fdd9250a3f00e92c84c)
2025-08-02 15:33:24,706 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=ac80cbfb03d1d987473509118cf1826c92aa6e802fb45777878a7d5e6a87cb12
2025-08-02 15:33:24,707 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=ac80cbfb03d1d987473509118cf1826c92aa6e802fb45777878a7d5e6a87cb12 (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=ac80cbfb03d1d987473509118cf1826c92aa6e802fb45777878a7d5e6a87cb12)
2025-08-03 00:55:32,711 - discord.gateway - WARNING - Can't keep up, shard ID None websocket is 43.5s behind.
2025-08-03 12:06:11,086 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376102595533213726/messages/1397130631867138151 responded with 429. Retrying in 0.41 seconds.
2025-08-03 12:06:12,117 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376102595533213726/messages/1397130157403275276 responded with 429. Retrying in 0.38 seconds.
2025-08-03 12:06:13,117 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376102595533213726/messages/1397126948593078333 responded with 429. Retrying in 0.38 seconds.
2025-08-03 12:06:14,087 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376102595533213726/messages/1396410540909396062 responded with 429. Retrying in 0.41 seconds.
2025-08-03 12:06:15,210 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376102595533213726/messages/1396404877017743360 responded with 429. Retrying in 0.30 seconds.
2025-08-03 12:06:16,160 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376102595533213726/messages/1396404346916175892 responded with 429. Retrying in 0.33 seconds.
2025-08-03 12:06:17,150 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376102595533213726/messages/1396404192993611856 responded with 429. Retrying in 0.35 seconds.
2025-08-03 12:06:18,314 - handlers.discord.admin.admin_commands - WARNING - Admin fantastic_beagle_96376 cleared 14 messages from #📝-bot-logs (keep_pinned: True)
2025-08-03 12:07:33,141 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376102562951860294/messages/1396021939482525747 responded with 429. Retrying in 0.52 seconds.
2025-08-03 12:07:34,315 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376102562951860294/messages/1394625205544030270 responded with 429. Retrying in 0.34 seconds.
2025-08-03 12:07:35,225 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376102562951860294/messages/1394550764101173349 responded with 429. Retrying in 0.43 seconds.
2025-08-03 12:07:36,284 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376102562951860294/messages/1394348243890081862 responded with 429. Retrying in 0.37 seconds.
2025-08-03 12:07:37,310 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376102562951860294/messages/1394314501326901280 responded with 429. Retrying in 0.35 seconds.
2025-08-03 12:07:38,278 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376102562951860294/messages/1394306129437266013 responded with 429. Retrying in 0.38 seconds.
2025-08-03 12:07:39,221 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376102562951860294/messages/1394299896911233075 responded with 429. Retrying in 0.43 seconds.
2025-08-03 12:07:40,229 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376102562951860294/messages/1394294687808098334 responded with 429. Retrying in 0.42 seconds.
2025-08-03 12:07:41,264 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376102562951860294/messages/1394294643931222138 responded with 429. Retrying in 0.39 seconds.
2025-08-03 12:07:42,319 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376102562951860294/messages/1394294628097724496 responded with 429. Retrying in 0.34 seconds.
2025-08-03 12:07:43,279 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376102562951860294/messages/1394291607804579902 responded with 429. Retrying in 0.38 seconds.
2025-08-03 12:07:44,273 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376102562951860294/messages/1394283267292921856 responded with 429. Retrying in 0.38 seconds.
2025-08-03 12:07:45,384 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376102562951860294/messages/1394283046282334258 responded with 429. Retrying in 0.30 seconds.
2025-08-03 12:07:46,373 - handlers.discord.admin.admin_commands - WARNING - Admin fantastic_beagle_96376 cleared 20 messages from #💬-general (keep_pinned: True)
2025-08-03 12:15:24,798 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376102516969967636/messages/1383429691075199101 responded with 429. Retrying in 0.55 seconds.
2025-08-03 12:15:26,002 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376102516969967636/messages/1379819461779128454 responded with 429. Retrying in 0.35 seconds.
2025-08-03 12:15:27,001 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376102516969967636/messages/1379818401748357172 responded with 429. Retrying in 0.35 seconds.
2025-08-03 12:15:28,076 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376102516969967636/messages/1379812969747316757 responded with 429. Retrying in 0.30 seconds.
2025-08-03 12:15:29,928 - handlers.discord.admin.admin_commands - WARNING - Admin fantastic_beagle_96376 cleared 11 messages from #📢-announcements (keep_pinned: True)
2025-08-03 12:15:53,752 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1390961458141069384/messages/1390961806830469191 responded with 429. Retrying in 0.45 seconds.
2025-08-03 12:15:54,874 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1390961458141069384/messages/1390961717479080029 responded with 429. Retrying in 0.33 seconds.
2025-08-03 12:15:56,063 - handlers.discord.admin.admin_commands - WARNING - Admin fantastic_beagle_96376 cleared 9 messages from #note (keep_pinned: True)
2025-08-03 13:13:44,169 - __main__ - WARNING - Slow command execution: watchlist took 6.66s
2025-08-03 19:59:41,962 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=f86c0fcff622b7b79c69c10760505a9d0298540dbca137e0aa0f16c72ded5fd5
2025-08-03 19:59:41,962 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=f86c0fcff622b7b79c69c10760505a9d0298540dbca137e0aa0f16c72ded5fd5 (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=f86c0fcff622b7b79c69c10760505a9d0298540dbca137e0aa0f16c72ded5fd5)
2025-08-04 00:20:46,628 - services.market.market_service - ERROR - Error fetching OHLCV data for BNBUSDT: binance GET https://fapi.binance.com/fapi/v1/klines?interval=1d&limit=21&symbol=BNBUSDT
2025-08-04 03:42:02,080 - discord.client - ERROR - Attempting a reconnect in 0.50s
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/discord/client.py", line 659, in connect
    await self.ws.poll_event()
  File "/usr/local/lib/python3.10/dist-packages/discord/gateway.py", line 646, in poll_event
    raise ConnectionClosed(self.socket, shard_id=self.shard_id, code=code) from None
discord.errors.ConnectionClosed: Shard ID None WebSocket closed with 1000
2025-08-04 04:36:24,116 - handlers.discord.admin.admin_commands - WARNING - Admin fantastic_beagle_96376 cleared 3 messages from #trade (keep_pinned: True)
2025-08-04 04:36:34,806 - handlers.discord.admin.admin_commands - WARNING - Admin fantastic_beagle_96376 cleared 4 messages from #📝-bot-logs (keep_pinned: True)
2025-08-04 04:46:59,250 - __main__ - WARNING - Slow command execution: watchlist took 6.56s
2025-08-04 06:40:39,772 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 10 seconds.
Loop thread traceback (most recent call last):
  File "/root/chartfix/bot.py", line 556, in <module>
    asyncio.run(main())
  File "/usr/lib/python3.10/asyncio/runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "/usr/lib/python3.10/asyncio/base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "/usr/lib/python3.10/asyncio/base_events.py", line 603, in run_forever
    self._run_once()
  File "/usr/lib/python3.10/asyncio/base_events.py", line 1909, in _run_once
    handle._run()
  File "/usr/lib/python3.10/asyncio/events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "/root/chartfix/services/trading/data_service.py", line 64, in _update_loop
    await self.update_all_data()
  File "/root/chartfix/services/trading/data_service.py", line 128, in update_all_data
    orders_result = self.trading_service.get_open_orders()
  File "/root/chartfix/services/core/error_service.py", line 83, in wrapper
    return func(*args, **kwargs)
  File "/root/chartfix/services/trading/trading_service.py", line 335, in get_open_orders
    orders = self.exchange.fetch_open_orders(formatted_symbol)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/binance.py", line 6739, in fetch_open_orders
    response = self.fapiPrivateGetOpenOrders(self.extend(request, params))
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/types.py", line 35, in unbound_method
    return _self.request(self.path, self.api, self.method, params, config=self.config)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/binance.py", line 11330, in request
    response = self.fetch2(path, api, method, params, headers, body, config)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/exchange.py", line 4386, in fetch2
    return self.fetch(request['url'], request['method'], request['headers'], request['body'])
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/exchange.py", line 551, in fetch
    response = self.session.request(
  File "/usr/local/lib/python3.10/dist-packages/requests/sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
  File "/usr/local/lib/python3.10/dist-packages/requests/sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/requests/adapters.py", line 486, in send
    resp = conn.urlopen(
  File "/usr/lib/python3/dist-packages/urllib3/connectionpool.py", line 700, in urlopen
    httplib_response = self._make_request(
  File "/usr/lib/python3/dist-packages/urllib3/connectionpool.py", line 441, in _make_request
    httplib_response = conn.getresponse()
  File "/usr/lib/python3.10/http/client.py", line 1375, in getresponse
    response.begin()
  File "/usr/lib/python3.10/http/client.py", line 318, in begin
    version, status, reason = self._read_status()
  File "/usr/lib/python3.10/http/client.py", line 279, in _read_status
    line = str(self.fp.readline(_MAXLINE + 1), "iso-8859-1")
  File "/usr/lib/python3.10/socket.py", line 705, in readinto
    return self._sock.recv_into(b)
  File "/usr/lib/python3.10/ssl.py", line 1303, in recv_into
    return self.read(nbytes, buffer)
  File "/usr/lib/python3.10/ssl.py", line 1159, in read
    return self._sslobj.read(len, buffer)

2025-08-04 06:40:40,463 - services.trading.trading_service - ERROR - Get orders error: binance {"code":-1021,"msg":"Timestamp for this request is outside of the recvWindow."}
2025-08-04 06:44:12,593 - services.trading.trading_service - ERROR - Network error fetching balance: binance {"code":-1021,"msg":"Timestamp for this request is outside of the recvWindow."}
2025-08-04 06:44:12,594 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance {"code":-1021,"msg":"Timestamp for this request is outside of the recvWindow."} (Caused by: InvalidNonce: binance {"code":-1021,"msg":"Timestamp for this request is outside of the recvWindow."})
2025-08-04 06:44:17,497 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 10 seconds.
Loop thread traceback (most recent call last):
  File "/root/chartfix/bot.py", line 556, in <module>
    asyncio.run(main())
  File "/usr/lib/python3.10/asyncio/runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "/usr/lib/python3.10/asyncio/base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "/usr/lib/python3.10/asyncio/base_events.py", line 603, in run_forever
    self._run_once()
  File "/usr/lib/python3.10/asyncio/base_events.py", line 1909, in _run_once
    handle._run()
  File "/usr/lib/python3.10/asyncio/events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "/root/chartfix/services/trading/data_service.py", line 64, in _update_loop
    await self.update_all_data()
  File "/root/chartfix/services/trading/data_service.py", line 128, in update_all_data
    orders_result = self.trading_service.get_open_orders()
  File "/root/chartfix/services/core/error_service.py", line 83, in wrapper
    return func(*args, **kwargs)
  File "/root/chartfix/services/trading/trading_service.py", line 335, in get_open_orders
    orders = self.exchange.fetch_open_orders(formatted_symbol)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/binance.py", line 6739, in fetch_open_orders
    response = self.fapiPrivateGetOpenOrders(self.extend(request, params))
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/types.py", line 35, in unbound_method
    return _self.request(self.path, self.api, self.method, params, config=self.config)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/binance.py", line 11330, in request
    response = self.fetch2(path, api, method, params, headers, body, config)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/exchange.py", line 4374, in fetch2
    self.throttle(cost)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/exchange.py", line 463, in throttle
    time.sleep(delay / 1000.0)

2025-08-04 07:34:26,691 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 10 seconds.
Loop thread traceback (most recent call last):
  File "/root/chartfix/bot.py", line 556, in <module>
    asyncio.run(main())
  File "/usr/lib/python3.10/asyncio/runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "/usr/lib/python3.10/asyncio/base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "/usr/lib/python3.10/asyncio/base_events.py", line 603, in run_forever
    self._run_once()
  File "/usr/lib/python3.10/asyncio/base_events.py", line 1909, in _run_once
    handle._run()
  File "/usr/lib/python3.10/asyncio/events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "/root/chartfix/services/trading/data_service.py", line 64, in _update_loop
    await self.update_all_data()
  File "/root/chartfix/services/trading/data_service.py", line 80, in update_all_data
    account_result = self.trading_service.get_account_balance()
  File "/root/chartfix/services/core/error_service.py", line 83, in wrapper
    return func(*args, **kwargs)
  File "/root/chartfix/services/core/error_service.py", line 171, in sync_wrapper
    return func(*args, **kwargs)
  File "/root/chartfix/services/trading/trading_service.py", line 67, in get_account_balance
    account_info = self.exchange.fapiPrivateV3GetAccount()
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/types.py", line 35, in unbound_method
    return _self.request(self.path, self.api, self.method, params, config=self.config)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/binance.py", line 11330, in request
    response = self.fetch2(path, api, method, params, headers, body, config)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/exchange.py", line 4386, in fetch2
    return self.fetch(request['url'], request['method'], request['headers'], request['body'])
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/exchange.py", line 551, in fetch
    response = self.session.request(
  File "/usr/local/lib/python3.10/dist-packages/requests/sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
  File "/usr/local/lib/python3.10/dist-packages/requests/sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/requests/adapters.py", line 486, in send
    resp = conn.urlopen(
  File "/usr/lib/python3/dist-packages/urllib3/connectionpool.py", line 700, in urlopen
    httplib_response = self._make_request(
  File "/usr/lib/python3/dist-packages/urllib3/connectionpool.py", line 441, in _make_request
    httplib_response = conn.getresponse()
  File "/usr/lib/python3.10/http/client.py", line 1375, in getresponse
    response.begin()
  File "/usr/lib/python3.10/http/client.py", line 318, in begin
    version, status, reason = self._read_status()
  File "/usr/lib/python3.10/http/client.py", line 279, in _read_status
    line = str(self.fp.readline(_MAXLINE + 1), "iso-8859-1")
  File "/usr/lib/python3.10/socket.py", line 705, in readinto
    return self._sock.recv_into(b)
  File "/usr/lib/python3.10/ssl.py", line 1303, in recv_into
    return self.read(nbytes, buffer)
  File "/usr/lib/python3.10/ssl.py", line 1159, in read
    return self._sslobj.read(len, buffer)

2025-08-04 07:34:33,797 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=a59339a3c61f4f5433794e4d5437b31e8efb709024f246cfb8b87c4c4507fb16
2025-08-04 07:34:33,798 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=a59339a3c61f4f5433794e4d5437b31e8efb709024f246cfb8b87c4c4507fb16 (Caused by: RequestTimeout: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=a59339a3c61f4f5433794e4d5437b31e8efb709024f246cfb8b87c4c4507fb16)
2025-08-04 07:34:36,693 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 20 seconds.
Loop thread traceback (most recent call last):
  File "/root/chartfix/bot.py", line 556, in <module>
    asyncio.run(main())
  File "/usr/lib/python3.10/asyncio/runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "/usr/lib/python3.10/asyncio/base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "/usr/lib/python3.10/asyncio/base_events.py", line 603, in run_forever
    self._run_once()
  File "/usr/lib/python3.10/asyncio/base_events.py", line 1909, in _run_once
    handle._run()
  File "/usr/lib/python3.10/asyncio/events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "/root/chartfix/services/trading/data_service.py", line 64, in _update_loop
    await self.update_all_data()
  File "/root/chartfix/services/trading/data_service.py", line 128, in update_all_data
    orders_result = self.trading_service.get_open_orders()
  File "/root/chartfix/services/core/error_service.py", line 83, in wrapper
    return func(*args, **kwargs)
  File "/root/chartfix/services/trading/trading_service.py", line 335, in get_open_orders
    orders = self.exchange.fetch_open_orders(formatted_symbol)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/binance.py", line 6739, in fetch_open_orders
    response = self.fapiPrivateGetOpenOrders(self.extend(request, params))
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/types.py", line 35, in unbound_method
    return _self.request(self.path, self.api, self.method, params, config=self.config)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/binance.py", line 11330, in request
    response = self.fetch2(path, api, method, params, headers, body, config)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/exchange.py", line 4374, in fetch2
    self.throttle(cost)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/exchange.py", line 463, in throttle
    time.sleep(delay / 1000.0)

2025-08-04 09:21:15,545 - discord.client - ERROR - Attempting a reconnect in 1.87s
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/discord/client.py", line 659, in connect
    await self.ws.poll_event()
  File "/usr/local/lib/python3.10/dist-packages/discord/gateway.py", line 646, in poll_event
    raise ConnectionClosed(self.socket, shard_id=self.shard_id, code=code) from None
discord.errors.ConnectionClosed: Shard ID None WebSocket closed with 1000
2025-08-04 10:06:19,910 - discord.client - ERROR - Attempting a reconnect in 0.83s
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/discord/client.py", line 659, in connect
    await self.ws.poll_event()
  File "/usr/local/lib/python3.10/dist-packages/discord/gateway.py", line 646, in poll_event
    raise ConnectionClosed(self.socket, shard_id=self.shard_id, code=code) from None
discord.errors.ConnectionClosed: Shard ID None WebSocket closed with 1000
2025-08-04 11:30:45,937 - discord.client - ERROR - Attempting a reconnect in 0.12s
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/discord/client.py", line 659, in connect
    await self.ws.poll_event()
  File "/usr/local/lib/python3.10/dist-packages/discord/gateway.py", line 646, in poll_event
    raise ConnectionClosed(self.socket, shard_id=self.shard_id, code=code) from None
discord.errors.ConnectionClosed: Shard ID None WebSocket closed with 1000
2025-08-04 12:05:00,504 - services.trading.trading_service - ERROR - Network error fetching balance: binance {"code":-1021,"msg":"Timestamp for this request is outside of the recvWindow."}
2025-08-04 12:05:00,671 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance {"code":-1021,"msg":"Timestamp for this request is outside of the recvWindow."} (Caused by: InvalidNonce: binance {"code":-1021,"msg":"Timestamp for this request is outside of the recvWindow."})
2025-08-04 12:11:24,838 - discord.gateway - WARNING - Shard ID None has stopped responding to the gateway. Closing and restarting.
2025-08-04 12:42:03,043 - __main__ - WARNING - Slow command execution: watchlist took 8.48s
2025-08-04 12:43:05,120 - asyncio - ERROR - Unclosed connector
connections: ['[(<aiohttp.client_proto.ResponseHandler object at 0x7f298d07c1c0>, 1898541.*********)]']
connector: <aiohttp.connector.TCPConnector object at 0x7f29a07ab220>
